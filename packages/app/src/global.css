.high-risk-node {
  .shape-body {
    .fill-dark {
      fill: #E54545;
    }
  }
}

.medium-risk-node {
  .shape-body {
    .fill-dark {
      fill: #F87272;
    }
  }
}

.low-risk-node {
  .shape-body {
    .fill-dark {
      fill: #FFAD1F;
    }
  }
}

.line-risk-node {
  .arrows {
    /* fill: linear-gradient(180deg, #E54545 0%, #FFBCBC 100%); */
    fill: #FFBCBC
  }
  .lines {
    /* fill: linear-gradient(180deg, #E54545 0%, #FFBCBC 100%); */
    fill: #FFBCBC
  }
}

@keyframes shine-node-high-risk {
  0% {
    fill: #e54545;
  }
  100% {
    fill: #e54545;
  }
}

.shine-node-high-risk {
  .base-stroke {
    animation: shine-node-high-risk 3s ease-in-out 1 forwards;
  }
  .base-fill {
    animation: shine-node-high-risk 3s ease-in-out 1 forwards;
  }
}

@keyframes shine-node-medium-risk {
  0% {
    fill: #ff8a2a;
  }
  100% {
    fill: #ff8a2a;
  }
}

.shine-node-medium-risk {
  .base-stroke {
    animation: shine-node-medium-risk 3s ease-in-out 1 forwards;
  }
  .base-fill {
    animation: shine-node-medium-risk 3s ease-in-out 1 forwards;
  }
}