export enum RiskColor {
  High = '#e54545',
  Medium = '#ff8a2a',
  Low = '#0abf5b',
  LowUsed = '#d6f4e4',
  UnSupport = '#e7eaef',
  Nodata = 'grey',
}

// 0.正常 绿色  1.低使用率 淡绿色    2.中度负载 中风险 橙色   3. 高度负载 高风险 红色
export const StatusColorMap = {
  3: RiskColor.High,
  2: RiskColor.Medium,
  1: RiskColor.LowUsed,
  0: RiskColor.Low,
  '-1': RiskColor.UnSupport,
};

export enum RiskClassNames {
  SHINE_NODE_HIGH_RISK = 'shine-node-high-risk',
  SHINE_NODE_MEDIUM_RISK = 'shine-node-medium-risk',
};
