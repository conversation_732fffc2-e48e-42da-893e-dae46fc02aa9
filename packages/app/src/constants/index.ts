import { t } from '@tea/app/i18n';

export enum RiskLevel {
  High = 3,
  Medium = 2,
  LowUsed = 1,
  Low = 0,
  UnSupport = -1,
}

export const riskTable = {
  [RiskLevel.High]: 'highNest', // HighRiskLevel = 3 高风险
  [RiskLevel.Medium]: 'middleNest', // MediumRiskLevel = 2 中风险
  [RiskLevel.LowUsed]: 'uselessNest', // LowUsedRiskLevel = 1 低使用率
  [RiskLevel.Low]: 'lowNest', // LowRiskLevel = 0 低风险
  [RiskLevel.UnSupport]: 'none', // NotSupportLevel = -1 不支持机型
};

export enum RegionsTypeEnum {
  'SINGLE' = 'single',
  'MULTIPLE' = 'multiple',
}

export const RegionsTypeName = {
  [RegionsTypeEnum.SINGLE]: t('单地域'),
  [RegionsTypeEnum.MULTIPLE]: t('多地域'),
};

export enum SegViewType {
  RISK = '0',
  ALL = '1',
}

export const periodValueType = {
  3: t('近三天'),
  7: t('近一周'),
  14: t('近两周'),
  30: t('近一个月'),
};

export enum ArchiveTypeEnum {
  ARCHIVE = 0,
  UNARCHIVE = -1,
}

export enum EnvEnum {
  CONSOLE = 'CONSOLE',
  ISA = 'ISA',
}

export enum EipAccountTypeEnum {
  标准账户 = 0,
  传统账户 = 1,
}

export enum UrlParams {
  REPORT_VIEW = 'reportView',
}

export enum LocalStorageKey {
  CAPACITY_SUBSCRIBE_TIP = 'capacity-subscribe-tip',
}

export enum ThresholdStatusEnum {
 NOT_APPLICABLE = -1,
}

export enum RiskLevelEnum {
  HIGH = 3,
  MEDIUM = 2,
  LOW = 1,
  NORMAL = 0,
}

export const INSIGHT_NODE = 'InsightNode';

export const GET_MONITOR_DATA_RESULT = 'GetMonitorDataResult';

export const GET_MONITOR_DATA = 'GetMonitorData';

// 确保枚举值在运行时可用
export const StatusTextMap = {
  loading: t('思考中...'),
  stopped: t('思考已终止'),
  finished: t('思考完成'),
};
