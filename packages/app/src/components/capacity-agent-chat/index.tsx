/**
 * @description AgentChat抽屉
 */
import React, { useState, useRef, useMemo, useEffect } from 'react';
import {
  Bubble, Button,
} from '@tencent/tea-component';
import { app } from '@tea/app';
import { Dialog } from 'tdesign-react';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { ClearIcon } from '@tencent/tea-icons-react';
import { archInfo as apis } from '@src/utils/caching';
import globalState from '@src/stores/global.state';
import { useHookstate } from '@hookstate/core';
import { v4 as uuidv4 } from 'uuid';
import { isEmpty, find, get } from 'lodash';
import { getPopupContainer } from '@src/utils/common';
import { baseURL } from '@src/utils/request';

import {
  useChatStore,
  Chat,
  // ChatOnboarding,
  Stack,
  ChatSuggestionList,
  // ChatSender,
} from '@tencent/cloud-chat-ui';
import { GET_MONITOR_DATA_RESULT, GET_MONITOR_DATA, INSIGHT_NODE } from '@src/constants';
import DatePicker from './components/date-picker';
import MessageItem from './components/message-item';
import { t } from '@tea/app/i18n';
import s from './index.module.scss';

export enum MessageTypeEnum {
  user = 'user',
  assistant = 'assistant',
}
export enum ContentType {
  text = 'text',
  image = 'image',
  iframeUrl = 'iframeUrl',
  chart = 'chart',
  default = 'default'
}

export enum ReasoningStatus {
  loading = 'loading',
  stopped = 'stopped',
  finished = 'finished'
}

export interface MessageType {
  end?: boolean;
  id?: string;
  type: MessageTypeEnum,
  content: any,
  contentType: ContentType;
  questions?: string[];
  deepThink?: string;
  requestId?: string;
  steps?: any;
  costTime?: number;
  reasoningStatus?: ReasoningStatus;
  mutiList?: {
    content: string;
    thought: string;
  }[];
}

interface IProps {
  visible: boolean;
  onCloseCallback?: () => void;
  messages?: ReadonlyArray<MessageType>;
}

const CapacityAgentChat = ({ visible, onCloseCallback, messages: initMessages = [] }: IProps) => {
  const global = useHookstate(globalState);
  const appId = location.search?.split('appid')?.[1]?.split('&')?.[0]?.split('=')?.[1] || '';
  const [messages, setMessages] = useState<MessageType[]>([...initMessages]);
  const [suggestionList, setSuggestionList] = useState([]);
  const { processing, setProcessing } = useChatStore();
  const cancelRef = useRef(null);
  const [sessionId, setSessionId] = useState<string>(uuidv4());
  const [confirmVisible, setConfirmVisible] = useState(false);
  const scrollerRef = useRef({});
  const [nodeMonitorData, setNodeMonitorData] = useState<any>({});
  const expandedHoverCardId = useHookstate(globalState.expandedHoverCardId).value;
  const timerRef = useRef<any>(null);
  const nodeCardMapRef = useRef<Record<string, any>>({});

  const chatIdRef = useRef('');
  const hasArchCapacityData = useRef(false);
  const hasPassedInitialMessagesSyncRef = useRef(false);

  const finalMessages = useMemo(() => messages?.filter(v => (v.type === MessageTypeEnum.user && v.content) || (v.type === MessageTypeEnum.assistant)), [messages]);
  const onClose = () => {
    setConfirmVisible(false);
  };
  const onCloseConfirm = () => {
    setConfirmVisible(false);
    globalState.set(state => ({
      ...state,
      agentDrawerVisible: false,
      agentRenderHoverCard: false,
      agentTimeLineBarVisible: false,
      agentTimeLineBarPlaying: false,
      agentCurrentTime: 0,
      agentShowAnalysisResult: false,
      agentShowResourceCapacity: false,
      agentShowNotificationBanner: false,
      expandedHoverCardId: '',
      agentDatePickerVisible: false,
    }));
    onCloseCallback?.();
  };

  // 处理sse异常
  const handleError = (str?: string, ctrl?: any) => {
    setProcessing(false);
    setMessages((prev) => {
      const lastContent = prev[prev.length - 1]?.content;
      return [...(prev.slice(0, prev.length - 1)), {
        type: MessageTypeEnum.assistant,
        content: `${lastContent}
          ${(str || t('查询失败,请稍后再试'))}
        `,
        contentType: ContentType.text,
        id: chatIdRef.current,
      }];
    });
    ctrl?.abort?.();
  };

  // 发送消息前对messages预处理
  const handleInitSendMessage = (v: string, options?: {
    addLast?: boolean; // 是否追加
    error?: string; // 报错信息
  }) => {
    const { addLast = false } = options || {};
    setSuggestionList([]);
    // 正常消息
    if (!addLast) {
      setMessages(prev => [...prev, {
        type: MessageTypeEnum.user,
        content: v,
        contentType: ContentType.text,
        id: uuidv4(),
      }, {
        id: uuidv4(),
        type: MessageTypeEnum.assistant,
        content: '',
        contentType: ContentType.text,
        deepThink: '',
        costTime: 0,
        reasoningStatus: ReasoningStatus.loading,
      }]);
    } else {
      setMessages(prev =>
        // 整理最后一条消息
        // eslint-disable-next-line implicit-arrow-linebreak
        [...(prev.slice(0, prev.length - 1)), {
          type: MessageTypeEnum.assistant,
          content: prev[prev.length - 1]?.content,
          contentType: ContentType.text,
          id: prev[prev.length - 1].id,
          costTime: prev[prev.length - 1]?.costTime ?? 0,
          reasoningStatus: prev[prev.length - 1]?.reasoningStatus ?? ReasoningStatus.loading,
        }]);
    }
  };

  const handleEventStep = (data, id, ctrl) => {
    try {
      const {
        StepKey: stepKey,
        StepData: result,
      } = data?.Data ?? {};
      if (stepKey === INSIGHT_NODE) {
        hasArchCapacityData.current = false;
      }
      if (stepKey === GET_MONITOR_DATA) {
        hasArchCapacityData.current = true;
        try {
          const cardData = JSON.parse(data?.Data?.StepData ?? '{}');
          const nodeUuid = cardData?.NodeUuid;
          if (!nodeUuid) return;
          const prev = nodeCardMapRef.current[nodeUuid];
          const toArray = (v) => {
            if (Array.isArray(v)) return v;
            return v ? [v] : [];
          };
          let merged = cardData;
          if (prev) {
            const prevItems = toArray(prev?.InstanceItems);
            const newItems = toArray(cardData?.InstanceItems);
            merged = {
              ...prev,
              ...cardData,
              InstanceItems: [...prevItems, ...newItems],
            };
          }
          nodeCardMapRef.current[nodeUuid] = merged;
          setNodeMonitorData(last => ({
            ...last,
            [nodeUuid]: merged,
          }));
        } catch (error) {
          console.log(error);
        }
      }
      if (stepKey === GET_MONITOR_DATA_RESULT) {
        hasArchCapacityData.current = true;
        const result = JSON.parse(data?.Data?.StepData ?? '{}');
        try {
          global.set(state => ({
            ...state,
            monitorDataResult: result,
          }));
        } catch (e) {
          console.log(e);
        }
      }
      setMessages((prev) => {
        const stepItem = find(prev[prev.length - 1]?.steps, item => item.stepKey === stepKey) || {};
        if (isEmpty(stepItem)) {
          const stepList = prev[prev.length - 1]?.steps || [];
          stepList.push({
            stepKey,
            stepContent: result,
          });
          return [...(prev.slice(0, prev.length - 1)), {
            id,
            type: MessageTypeEnum.assistant,
            contentType: ContentType.text,
            steps: stepList,
            deepThink: prev[prev.length - 1]?.deepThink,
            content: prev[prev.length - 1]?.content,
            costTime: prev[prev.length - 1]?.costTime,
            reasoningStatus: prev[prev.length - 1]?.reasoningStatus,
            mutiList: prev[prev.length - 1]?.mutiList ?? [],
          }];
        }
        const newSteps = prev[prev.length - 1]?.steps?.map((item) => {
          if (item.stepKey === stepKey) {
            return {
              stepKey,
              stepContent: result,
            };
          }
          return item;
        });
        return [...(prev.slice(0, prev.length - 1)), {
          id,
          type: MessageTypeEnum.assistant,
          contentType: ContentType.text,
          steps: newSteps,
          deepThink: prev[prev.length - 1]?.deepThink,
          content: prev[prev.length - 1]?.content,
          costTime: prev[prev.length - 1]?.costTime,
          reasoningStatus: prev[prev.length - 1]?.reasoningStatus,
          mutiList: prev[prev.length - 1]?.mutiList ?? [],
        }];
      });
    } catch (e) {
      handleError(e.message, ctrl);
      console.error('json error', e);
    }
  };

  const handleEventThought = (data, id, isMutiThought) => {
    if (isMutiThought) {
      setMessages((prev) => {
        const lastMessage = prev[prev.length - 1];
        const currentMutiList = lastMessage?.mutiList || [];

        // 检查最后一个思考组是否已经有content，如果有则创建新的组
        const lastGroup = currentMutiList[currentMutiList.length - 1];
        let updatedMutiList: { thought: string; content: string; }[];

        if (!lastGroup || lastGroup.content) {
          // 创建新的思考组
          updatedMutiList = [...currentMutiList, {
            thought: data?.Data ?? '',
            content: '',
          }];
        } else {
          // 更新当前组的thought
          updatedMutiList = [
            ...currentMutiList.slice(0, -1),
            {
              ...lastGroup,
              thought: (lastGroup.thought || '') + (data?.Data ?? ''),
            },
          ];
        }

        return [...(prev.slice(0, prev.length - 1)), {
          id,
          type: MessageTypeEnum.assistant,
          contentType: ContentType.text,
          steps: lastMessage?.steps,
          deepThink: lastMessage?.deepThink ?? '',
          costTime: lastMessage?.costTime ?? 0,
          content: lastMessage?.content,
          reasoningStatus: lastMessage?.reasoningStatus,
          mutiList: updatedMutiList,
        }];
      });
    } else {
      setMessages(prev => [...(prev.slice(0, prev.length - 1)), {
        id,
        type: MessageTypeEnum.assistant,
        contentType: ContentType.text,
        steps: prev[prev.length - 1]?.steps,
        deepThink: (prev[prev.length - 1]?.deepThink ?? '') + (data?.Data ?? ''),
        costTime: prev[prev.length - 1]?.costTime ?? 0,
        reasoningStatus: prev[prev.length - 1]?.reasoningStatus,
        content: prev[prev.length - 1]?.content,
      }]);
    }
  };
  const handleEventMessage = (parseData, id, isMutiThought) => {
    if (isMutiThought) {
      setMessages((prev) => {
        const lastMessage = prev[prev.length - 1];
        const currentMutiList = lastMessage?.mutiList || [];

        // 确保有至少一个思考组存在
        let updatedMutiList: { thought: string; content: string; }[];

        if (currentMutiList.length === 0) {
          // 如果没有思考组，创建一个空的思考组
          updatedMutiList = [{
            thought: '',
            content: parseData?.Data ?? '',
          }];
        } else {
          // 更新最后一个思考组的content
          const lastGroup = currentMutiList[currentMutiList.length - 1];
          updatedMutiList = [
            ...currentMutiList.slice(0, -1),
            {
              ...lastGroup,
              content: (lastGroup.content || '') + (parseData?.Data ?? ''),
            },
          ];
        }

        return [...(prev.slice(0, prev.length - 1)), {
          id,
          type: MessageTypeEnum.assistant,
          contentType: ContentType.text,
          steps: lastMessage?.steps,
          deepThink: lastMessage?.deepThink,
          content: lastMessage?.content,
          costTime: prev[prev.length - 1]?.costTime ?? 0,
          reasoningStatus: prev[prev.length - 1]?.reasoningStatus ?? ReasoningStatus.loading,
          mutiList: updatedMutiList,
        }];
      });
    } else {
      setMessages(prev => [...(prev.slice(0, prev.length - 1)), {
        id,
        type: MessageTypeEnum.assistant,
        contentType: ContentType.text,
        steps: prev[prev.length - 1]?.steps,
        deepThink: prev[prev.length - 1]?.deepThink,
        content: (prev[prev.length - 1]?.content ?? '') + (parseData?.Data ?? ''),
        costTime: prev[prev.length - 1]?.costTime ?? 0,
        reasoningStatus: prev[prev.length - 1]?.reasoningStatus ?? ReasoningStatus.loading,
        mutiList: prev[prev.length - 1]?.mutiList ?? [],
      }]);
    }
  };

  const handleEventEnd = (data, ctrl) => {
    ctrl.abort();
    // 有数据才展示卡片 播放控制器等
    if (hasArchCapacityData.current && globalState.get().agentDrawerVisible) {
      global.set(state => ({
        ...state,
        agentShowAnalysisResult: true,
        agentTimeLineBarVisible: true,
        agentRenderHoverCard: true,
      }));
    }
    setMessages((last) => {
      const thinkItem = last.find(v => v.type === MessageTypeEnum.assistant && v.steps.map(v => v.stepKey).every(v => [GET_MONITOR_DATA_RESULT, GET_MONITOR_DATA].includes(v)));
      if (thinkItem) {
        thinkItem.end = true;
      }
      return [...last];
    });
    setProcessing(false);
    setMessages((prev) => {
      if (prev.length === 0) return prev;
      const lastMessage = prev[prev.length - 1];
      if (lastMessage && lastMessage.type === MessageTypeEnum.assistant) {
        return [...(prev.slice(0, prev.length - 1)), {
          ...lastMessage,
          reasoningStatus: ReasoningStatus.finished,
        }];
      }
      return prev;
    });
  };

  // 处理接收到的消息
  const handleMessage = (params: {
    rsp: {data: string; event?: string; id?:string; retry?:any};
    ctrl: any;
    isMutiThought: boolean;
  }) => {
    const { rsp, ctrl, isMutiThought } = params;

    setProcessing(true);
    if (!rsp?.data) {
      // console.log(t('收到心跳:'), rsp);
      return;
    }

    try {
      const { data: rsData, event: rsEvent, id } = rsp;
      const parseData = JSON.parse(rsData);
      if (get(Response, 'Error.Code') === 'AuthFailure.UnauthorizedOperation'
        || get(Response, 'Error.Code') === 1) {
        handleError(get(Response, 'Error.Message'), ctrl);
        return;
      }
      switch (rsEvent) {
        case 'Step':
          handleEventStep(parseData, id, ctrl);
          break;
        case 'Thought':
          handleEventThought(parseData, id, isMutiThought);
          break;
        case 'Message':
          handleEventMessage(parseData, id, isMutiThought);
          break;
        case 'End':
          handleEventEnd(parseData, ctrl);
          break;
        default:
          console.warn('Unknown event type:');
      }
    } catch (e) {
      handleError(e.message, ctrl);
      console.error('json error', e);
    }
  };

  // 处理发送消核心逻辑
  const handleSendMessage = (
    v: string,
    intentionParam = {},
    intention: string = GET_MONITOR_DATA,
    isMutiThought: boolean = false,
    options?: {
      addLast?: boolean; // 是否追加
      error?: string; // 报错信息
    }
  ) => {
    if (processing) return;
    setProcessing(true);
    const { addLast = false } = options || {};
    globalState.set(state => ({
      ...state,
      agentInputValue: '',
    }));
    handleInitSendMessage(v, options);
    // 控制取消请求
    const ctrl = new AbortController();
    cancelRef.current = ctrl;
    const isConsole = apis?.env === 'CONSOLE';
    let url;
    let method;
    let headers;
    let body;
    const question = v;
    if (isConsole) {
      const info = app.capi.generateSseRequest({
        serviceType: 'saai',
        cmd: 'ChatCapacityCompletions',
        data: {
          Version: '2025-03-17',
          Message: question,
          SessionId: sessionId,
          ArchId: apis?.archInfo?.archId,
          Intention: intention,
          IntentionParam: intentionParam ? JSON.stringify(intentionParam) : '',
        },
        regionId: 1,
      });
      const {
        url: u, method: m, headers: h, body: b,
      } = info;
      url = u;
      method = m;
      headers = h;
      body = b;
    } else {
      // 运营端SSE请求
      const params = {
        AppId: +appId,
        ArchId: apis?.archInfo?.archId,
        Action: 'ChatCapacityCompletions',
        SessionId: sessionId,
        Message: question,
        Uin: apis?.uin,
        SubAccountUin: apis?.uin,
        Intention: intention,
        Language: 'zh-CN',
        IntentionParam: intentionParam ? JSON.stringify(intentionParam) : '',
      };
      url = `${baseURL}/sse`;
      method = 'post';
      headers = {
        'Content-Type': 'application/json',
      };
      body = JSON.stringify(params);
    }

    fetchEventSource(url, {
      method,
      headers,
      body,
      credentials: 'include',
      openWhenHidden: true,
      signal: ctrl.signal,
      // 必须设置，否则出现异常无法终止
      onopen(res): any {
        if (res.status !== 200) {
          handleError(t('网络异常'), ctrl);
        } else if (!addLast) {
          setMessages(prev => [...(prev.slice(0, prev.length - 1)), {
            type: MessageTypeEnum.assistant,
            content: '',
            contentType: ContentType.text,
            id: prev[prev.length - 1].id,
            costTime: prev[prev.length - 1]?.costTime ?? 0,
            reasoningStatus: prev[prev.length - 1]?.reasoningStatus ?? ReasoningStatus.loading,
          }]);
        }
      },
      onmessage(rsp) {
        handleMessage({
          rsp,
          ctrl,
          isMutiThought,
        });
      },
      onerror(e) {
        handleError(e.message, ctrl);
        console.error('sse error', e, ctrl);
      },
    });
  };

  const stopMessage = () => {
    if (!processing) return;
    cancelRef.current?.abort();
    setProcessing(false);
    setMessages((prev) => {
      if (prev.length === 0) return prev;
      const lastMessage = prev[prev.length - 1];
      if (lastMessage && lastMessage.type === MessageTypeEnum.assistant) {
        return [...(prev.slice(0, prev.length - 1)), {
          ...lastMessage,
          reasoningStatus: ReasoningStatus.stopped,
        }];
      }
      return prev;
    });
  };

  const handleSessionClear = () => {
    setSessionId(uuidv4());
    setMessages(prev => prev.map(m => ({
      ...m,
      costTime: m.type === MessageTypeEnum.assistant ? 0 : m.costTime,
      reasoningStatus: m.type === MessageTypeEnum.assistant ? ReasoningStatus.finished : m.reasoningStatus,
    })));
    globalState.set(state => ({
      ...state,
      agentRenderHoverCard: false,
      agentTimeLineBarVisible: false,
      agentTimeLineBarPlaying: false,
      agentCurrentTime: 0,
      agentShowAnalysisResult: false,
      agentShowResourceCapacity: false,
      agentShowNotificationBanner: false,
      expandedHoverCardId: '',
      agentDatePickerVisible: true,
    }));
    stopMessage();
    setMessages([]);
    nodeCardMapRef.current = {};
  };

  useEffect(() => {
    if (expandedHoverCardId) {
      if (!processing) return;
      cancelRef.current?.abort();
      setProcessing(false);
      // 切换卡片时，清空消息，只保留最后一条消息，用于展示根因分析
      setMessages(messages => messages.slice(1, 2));
    }
  }, [expandedHoverCardId]);

  useEffect(() => {
    if (Object.keys(nodeMonitorData).length) {
      global.set(state => ({
        ...state,
        monitorData: nodeMonitorData,
      }));
    }
  }, [nodeMonitorData]);

  // 计时器：为最后一条 assistant 消息增加 costTime
  useEffect(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    if (processing) {
      timerRef.current = setInterval(() => {
        setMessages((prev) => {
          if (!prev?.length) return prev;
          const last = prev[prev.length - 1];
          if (!last || last.type !== MessageTypeEnum.assistant) return prev;
          const updated = {
            ...last,
            costTime: (last.costTime ?? 0) + 1,
          };
          return [...prev.slice(0, prev.length - 1), updated];
        });
      }, 1000);
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [processing]);

  useEffect(() => {
    if (!hasPassedInitialMessagesSyncRef.current) {
      hasPassedInitialMessagesSyncRef.current = true;
      return;
    }
    globalState.set(state => ({
      ...state,
      agentMessages: messages,
    }));
  }, [messages]);

  return (
    <div>
      <Chat
        minWidth={425}
        title={
          <div className={s['chat-title-drawer']}>
            <span>{t('容量回放助手')}</span>
            <div className={s['chat-action-tag']}>Beta</div>
          </div>
        }
        visible={visible}
        portalContainer={getPopupContainer()}
        isInSessions={finalMessages.length > 0}
        fixedTop={50}
        className={s['chat-drawer-container']}
        bounds={{ top: 100, left: 100, right: 10, bottom: 30 }}
        logo={null}
        onCloseButtonClick={() => {
          if (global.agentTimeLineBarPlaying.get()) {
            setConfirmVisible(true);
          } else {
            globalState.set(state => ({
              ...state,
              agentDrawerVisible: false,
              agentRenderHoverCard: false,
              agentTimeLineBarVisible: false,
              agentTimeLineBarPlaying: false,
              agentCurrentTime: 0,
              agentShowAnalysisResult: false,
              agentShowResourceCapacity: false,
              agentShowNotificationBanner: false,
              expandedHoverCardId: '',
              agentDatePickerVisible: false,
            }));
            onCloseCallback?.();
          }
        }}
        // inputArea={
        //   <>
        //     <ChatSender
        //       getTextareaRef={(node) => {
        //         textareaRef.current = node;
        //       }}
        //       value={globalState.get().agentInputValue}
        //       loading={processing}
        //       topExtra={
        //         <>
        //         </>
        //       }
        //       onChange={(v) => {
        //         globalState.set(state => ({
        //           ...state,
        //           agentInputValue: v,
        //         }));
        //       }}
        //       onSend={(v) => {
        //         if (v.trim().length <= 0) {
        //           return tip.warning({
        //             content: t('请勿使用空白信息进行询问'),
        //           });
        //         }
        //         if (v.trim().length > 1024) {
        //           return tip.warning({
        //             content: t('消息长度不能超过1024个字符'),
        //           });
        //         }

        //         handleSendMessage(v);
        //       }}
        //       onCancelProcess={stopMessage}
        //     />
        //   </>
        // }
        actions={[
          <Bubble
            arrowPointAtCenter
            placement="bottom"
            className={s['chat-ui__bubble']}
            openDelay={100}
            content={t('清空会话')}
            key={'normal'}
          >
            <Button type="text" onClick={handleSessionClear}>
              <ClearIcon />
            </Button>
          </Bubble>,
        ]}
        scrollerRef={scrollerRef}
      >
        {finalMessages.length > 0 ? (
          <Stack>
            {finalMessages.map((item, index) => (
              <MessageItem
                key={index}
                item={item}
                processing={processing}
                handleSendMessage={handleSendMessage}
              />
            ))}
            {!processing && finalMessages.length > 0 && (
              <ChatSuggestionList list={suggestionList} />
            )}
          </Stack>
        ) // : <ChatOnboarding
        //   subTitle={t('我是容量Agent')}
        // />
          : null}

        {global.agentDatePickerVisible.get() && (
          <DatePicker handleSendMessage={handleSendMessage} onCancel={onCloseCallback} />
        )}

        <Dialog
          header={t('容量回放进行中，您确认要离开吗？')}
          theme="info"
          confirmBtn={t('确认离开')}
          visible={confirmVisible}
          onClose={onClose}
          onConfirm={onCloseConfirm}
        >
          <p>{t('容量回放进行中，若现在离开则将丢失当前进度，您确认要离开吗？')}</p>
        </Dialog>
      </Chat>
    </div>
  );
};
export default CapacityAgentChat;
