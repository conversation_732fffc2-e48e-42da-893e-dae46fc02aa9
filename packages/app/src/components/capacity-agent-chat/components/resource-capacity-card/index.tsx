import React, { useState, useRef, useMemo } from 'react';
import resourceDistribute from '@src/statics/svg/resource-distribute.svg';
import { t } from '@tea/app/i18n';
import {
  Icon,
  Bubble,
  Row,
  Col,
  Text,
  StatusTip,
  Tooltip,
} from '@tencent/tea-component';
import { ClickHouseConfig } from '@src/components/archive-report/constants';
import { MetricView } from '@src/components/bee-nest/metric-view';
import { describeInsResourceRealTimeInfo } from '@src/api/nodeDrawer';
import { useHookstate } from '@hookstate/core';
import globalState from '@src/stores/global.state';
// import { RiskLevel } from '@src/constants';
import { RiskColor, StatusColorMap } from '@src/constants/color';


import s from './index.module.scss';
import nodata from '@src/statics/svg/nodata.svg';

const { LoadingTip } = StatusTip;
type IHoverData = {
  hoverInfo: any[],
  metricInfo: { Metric: string, Value: number, MetricRiskLevel: number }[],
  regionList: any[];
}

const emptyHoverData: IHoverData = {
  hoverInfo: [],
  metricInfo: [],
  regionList: [],
};

interface MetricItem {
  label: string;
  value: number;
  color: string;
  dotColor: string;
}

interface ResourceCapacityCardProps {
  title?: string;
  className?: string;
  product?: string;
  // ccn信息
  ccnInfo?: {
    isCcn: boolean;
  };
}

// UI 依赖外部实时数据，默认指标不再需要

const ResourceCapacityCard: React.FC<ResourceCapacityCardProps> = ({
  title = t('资源容量分布'),
  className = '',
  product,
  ccnInfo,
}) => {
  const sliceLength = 3;
  const expandedHoverCardId = useHookstate(globalState.expandedHoverCardId);
  const monitorData = useHookstate(globalState.monitorData);
  const agentCurrentTime = useHookstate(globalState.agentCurrentTime).value;
  const data = monitorData[expandedHoverCardId.value]?.value;
  const monitorDataResult = useHookstate(globalState.monitorDataResult);

  // 添加显示状态管理
  const [showAllMetrics, setShowAllMetrics] = useState(false);
  // 选中实例（默认第一个）
  const [selectedInstanceId, setSelectedInstanceId] = useState<string | null>(null);

  // 统一的时间索引计算，供指标与蜂窝使用
  const timestampsSec = useMemo(() => (monitorDataResult.value as any)?.Timestamps || [], [monitorDataResult]);
  // 不再需要 periodSec 与毫秒级数组
  const currentIndex = useMemo(() => {
    if (!timestampsSec?.length) return -1;
    return (monitorDataResult.value as any).Timestamps.indexOf(Math.floor(agentCurrentTime / 1000));
  }, [agentCurrentTime, monitorDataResult.value, timestampsSec]);

  // 数字保留两位小数
  const roundToTwo = (n: any) => {
    if (typeof n !== 'number' || !Number.isFinite(n)) return n;
    return Math.round(n * 100) / 100;
  };

  // 实例列表
  const instanceItems = useMemo(() => (data?.InstanceItems || []) as any[], [data]);

  // 通用：根据当前时间点，按 lvl 降序、value 降序对 MetricItems 排序
  const sortMetricItemsByLevelAndValue = React.useCallback((metricItems: any[]) => (
    (metricItems || [])
      .slice()
      .sort((a: any, b: any) => {
        const va = currentIndex >= 0 ? a?.Values?.[currentIndex] : undefined;
        const vb = currentIndex >= 0 ? b?.Values?.[currentIndex] : undefined;
        const la = currentIndex >= 0 ? a?.Levels?.[currentIndex] : undefined;
        const lb = currentIndex >= 0 ? b?.Levels?.[currentIndex] : undefined;
        if ((la || 0) !== (lb || 0)) return (lb || 0) - (la || 0);
        return (vb || 0) - (va || 0);
      })
  ), [currentIndex]);

  // 将实例的 MetricItems 转成页面指标
  const buildMetricItemsFromInstance = (ins: any): MetricItem[] => {
    const list = sortMetricItemsByLevelAndValue(ins?.MetricItems)
      .map((m: any) => {
        const v = currentIndex >= 0 ? m?.Values?.[currentIndex] : undefined;
        const lvl = currentIndex >= 0 ? m?.Levels?.[currentIndex] : undefined;
        const color = StatusColorMap[lvl as keyof typeof StatusColorMap] || RiskColor.Low;
        const dotColor = StatusColorMap[lvl as keyof typeof StatusColorMap] || RiskColor.Low;
        return {
          label: m?.Name || m?.Metric || t('未知指标'),
          value: roundToTwo(v ?? 0),
          color,
          dotColor,
        } as MetricItem;
      })
      .filter((m: any) => m.value !== undefined && m.value !== -1);
    return list;
  };

  // 默认选中第一个实例
  React.useEffect(() => {
    if (!selectedInstanceId && instanceItems.length > 0) {
      setSelectedInstanceId(instanceItems[0]?.InstanceId || null);
      setShowAllMetrics(false);
    }
  }, [instanceItems, selectedInstanceId]);

  // 当 expandedHoverCardId 变化（切换节点）时，重置为新节点的第一个实例并收起
  React.useEffect(() => {
    const first = (data?.InstanceItems && data.InstanceItems[0]?.InstanceId) || null;
    setSelectedInstanceId(first);
    setShowAllMetrics(false);
  }, [expandedHoverCardId.value]);

  // 选中实例的指标，随时间变化自动更新
  const selectedInstanceMetrics = useMemo(() => {
    if (!selectedInstanceId) return null;
    const ins = instanceItems.find((i: any) => i?.InstanceId === selectedInstanceId);
    if (!ins) return null;
    return buildMetricItemsFromInstance(ins);
  }, [selectedInstanceId, instanceItems, currentIndex]);

  // 基于选中实例，得到用于展示的基础指标集合（无回退）
  const baseMetrics = useMemo(() => selectedInstanceMetrics || [], [selectedInstanceMetrics]);

  const displayedMetrics = useMemo(() => baseMetrics.slice(0, showAllMetrics ? baseMetrics.length : sliceLength), [baseMetrics, showAllMetrics]);

  // 计算当前展示的指标中文本列需要的最大宽度（不超过120px）
  const labelColumnWidthPx = useMemo(() => {
    const base = 12;
    if (!baseMetrics || baseMetrics.length === 0) return 0;
    const list = baseMetrics.slice(0, showAllMetrics ? baseMetrics.length : sliceLength);
    if (!list.length) return 0;
    // 与样式保持一致：font-weight: 500; font-size: 12px; font-family: 'PingFang SC'
    const font = '500 12px \'PingFang SC\', -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, \'Helvetica Neue\', Arial, \'Noto Sans\'';
    const measure = (text: string) => {
      if (typeof document === 'undefined') return text.length * base;
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) return text.length * base;
      ctx.font = font;
      const metrics = ctx.measureText(text);
      return Math.ceil(metrics.width);
    };
    const maxTextWidth = list.reduce((acc, cur) => Math.max(acc, measure(cur.label || '')), 0);
    // 补偿圆点与间距的总宽度：dot(8px) + gap(5px) + 缓冲(2px)
    const extra = 8 + 5 + 2;
    const totalWidth = maxTextWidth + extra;
    return Math.min(120, totalWidth);
  }, [baseMetrics, showAllMetrics, sliceLength]);

  // 处理查看更多/收起的点击事件
  const handleToggleMetrics = () => {
    setShowAllMetrics(!showAllMetrics);
  };

  const timeoutId = useRef(null);
  const isTcHouse = ClickHouseConfig.CLICK_HOUSE === product;
  const renderHexagons = () => {
    const instanceItems = (data?.InstanceItems || []) as any[];
    const [hoverData, setHoverData] = useState<IHoverData>(emptyHoverData);
    const [selectNest, setSelectNest] = useState(0);

    // 当切换节点或实例列表变化时，重置蜂窝本地选中为第一个
    React.useEffect(() => {
      setSelectNest(0);
    }, [expandedHoverCardId.value, instanceItems.length]);

    const buildHoverDataFromInstance = (ins: any) => {
      const metricInfo = (ins?.MetricItems || [])
        .map((m: any) => {
          const v = currentIndex >= 0 ? m?.Values?.[currentIndex] : undefined;
          const lvl = currentIndex >= 0 ? m?.Levels?.[currentIndex] : undefined;
          return {
            Metric: m?.Name || m?.Metric || t('未知指标'),
            Value: roundToTwo(v ?? -1),
            MetricRiskLevel: typeof lvl === 'number' ? lvl : 0,
          };
        })
        .filter((m: any) => m.Value !== undefined && m.Value !== -1);
      clearTimeout(timeoutId.current);
      timeoutId.current = setTimeout(() => {
        getDescribeInsResourceRealTimeInfo({  ...ins, metricInfo });
      }, 300);
    };

    // 将实例的 MetricItems 转成页面指标
    const buildMetricItemsFromInstance = (ins: any): MetricItem[] => {
      const list = sortMetricItemsByLevelAndValue(ins?.MetricItems)
        .map((m: any) => {
          const v = currentIndex >= 0 ? m?.Values?.[currentIndex] : undefined;
          const lvl = currentIndex >= 0 ? m?.Levels?.[currentIndex] : undefined;
          let color = '#22C55E';
          let dotColor = '#22C55E';
          if (lvl === 1 || lvl === 2 || lvl === 3) {
            color = '#FF6B35';
            dotColor = '#FF4444';
          }
          return {
            label: m?.Name || m?.Metric || t('未知指标'),
            value: roundToTwo(v ?? 0),
            color,
            dotColor,
          } as MetricItem;
        })
        .filter((m: any) => m.value !== undefined && m.value !== -1);
      return list;
    };
    // 查询实例资源信息，hover时调用
    const getDescribeInsResourceRealTimeInfo = async (data) => {
      try {
        const res = await describeInsResourceRealTimeInfo({
          MapId: globalState.archId.value,
          NodeUuid: expandedHoverCardId.value,
          InsId: data.InstanceId,
        });
        if (!res?.Error) {
          // 保存当前hover数据，只保留 ShowType为 1 3
          const hoverInfo = (res.InsResourceInfoList || []).filter(i => i.ShowType === 3 || i.ShowType === 1);
          setHoverData(last => ({
            ...last,
            hoverInfo,
            metricInfo: data?.metricInfo,
            regionList: res?.RegionList ?? [],
          }));
        }
      } catch (err) {
        console.log(err);
      }
    };
    const handleMouseEnter = (ins: any) => {
      setHoverData(emptyHoverData);
      timeoutId.current = setTimeout(() => {
        buildHoverDataFromInstance(ins);
      }, 0);
    };

    const handleMouseLeave = () => {
      clearTimeout(timeoutId.current);
    };

    // 仅展示前 100 个蜂窝
    const displayedCount = Math.min(instanceItems.length, 100);

    const containerRef = useRef<HTMLDivElement | null>(null);
    const [beforeHeightPx, setBeforeHeightPx] = useState<number>(50);

    const recalcBeforeHeight = React.useCallback(() => {
      const container = containerRef.current;
      if (!container) return;
      const wrappers = Array.from(container.getElementsByClassName(s['hexagon-wrapper'])) as HTMLElement[];
      const considered = wrappers.slice(0, displayedCount);
      if (considered.length === 0) {
        setBeforeHeightPx(50);
        return;
      }
      const rowTops = new Set<number>();
      considered.forEach(el => rowTops.add(el.offsetTop));
      const rows = rowTops.size || 1;
      const px = Math.max(50, rows * 33);
      setBeforeHeightPx(px);
    }, [displayedCount]);

    React.useEffect(() => {
      // 等待一帧以确保 DOM 渲染后再测量
      const id = requestAnimationFrame(recalcBeforeHeight);
      return () => cancelAnimationFrame(id);
    }, [recalcBeforeHeight]);

    React.useEffect(() => {
      const container = containerRef.current;
      if (!container) return;
      const onResize = () => recalcBeforeHeight();
      let ro: ResizeObserver | null = null;
      if (typeof ResizeObserver !== 'undefined') {
        ro = new ResizeObserver(onResize);
        ro.observe(container);
      } else {
        window.addEventListener('resize', onResize);
      }
      return () => {
        if (ro) ro.disconnect();
        else window.removeEventListener('resize', onResize);
      };
    }, [recalcBeforeHeight]);

    // 计算当前时间点所有指标的最大风险等级
    const getMaxRiskLevel = (metricItems: any[]) => {
      if (!metricItems || currentIndex < 0) return 0;
      const levels = metricItems
        .map((metric: any) => metric?.Levels?.[currentIndex])
        .filter((level: any) => level !== undefined && level !== null);
      return levels.length > 0 ? Math.max(...levels) : 0;
    };

    // 根据风险等级获取颜色
    const getHexagonColor = (level: number) => StatusColorMap[level as keyof typeof StatusColorMap] || RiskColor.Low;


    const memoInstanceItems = useMemo(() => {
      const mapped = instanceItems.map((ins: any) => {
        const maxLevel = getMaxRiskLevel(ins?.MetricItems || []);
        const hexagonColor = getHexagonColor(maxLevel);
        return { ...ins, maxLevel, hexagonColor };
      });
      return mapped.sort((a: any, b: any) => (b.maxLevel ?? 0) - (a.maxLevel ?? 0));
    }, [instanceItems, currentIndex]);
    return (
      <div
        ref={containerRef}
        className={s['hexagon-container']}
        style={{ '--hex-before-height': `${beforeHeightPx}px` } as React.CSSProperties}
      >
        {memoInstanceItems.slice(0, 100).map((ins, index) => {
          const maxLevel = ins?.maxLevel ?? 0;
          const hexagonColor = ins?.hexagonColor ?? getHexagonColor(maxLevel);
          return (
          <Bubble
            key={`hexagon-bubble-${index}`}
            arrowPointAtCenter
            trigger={'hover'}
            transitionTimeout={{
              enter: 0,
              exit: 0,
            }}
            placement="bottom-end"
            className="beeNestBubble"
            content={
              hoverData.hoverInfo.length || hoverData.metricInfo.length ? (
                <>
                  <Row>
                    {hoverData.hoverInfo.map((i, ind) => (
                      <Col key={`colH-${ind}`} span={24} className="baseInfoCol">
                        <div className="baseInfoKey">
                          {t('{{Key}}', { Key: i.Key })}
                        </div>
                        {i.Value ? (
                          <Text
                            overflow
                            onClick={() => {
                              i.IsSkip && i.Url && window.open(i.Url);
                            }}
                            style={{ cursor: 'pointer' }}
                            theme={i.IsSkip ? 'primary' : 'text'}
                            copyable={i.IsCopy}
                            tooltip={
                              <>
                                {i.Value.split('\n').length > 1
                                  ? i.Value.split('\n').map((val, i) => (
                                      <div key={`divVal-${i}`}>
                                        {t('{{Value}}', { Value: val })}
                                      </div>
                                  ))
                                  : t('{{Value}}', { Value: i.Value })}
                              </>
                            }
                          >
                            {t('{{Value}}', { Value: i.Value })}
                          </Text>
                        ) : (
                          t('暂无')
                        )}
                      </Col>
                    ))}
                  </Row>
                  <MetricView metricInfo={hoverData.metricInfo} ccnInfo={ccnInfo} hoverData={hoverData} />
                </>
              ) : (
                <div className="loadingWrap">
                  <LoadingTip />
                </div>
              )
            }
          >
            <div
              key={index}
              className={`${s['hexagon-wrapper']} ${selectNest === index ? s['select-nest'] : ''}`}
              onClick={() => {
                setSelectNest(index);
                // 点击蜂窝：展示该实例的 MetricItems
                buildMetricItemsFromInstance(ins);
                setSelectedInstanceId(ins?.InstanceId || null);
                // 切换查看更多时默认收起
                setShowAllMetrics(false);
              }}
              onMouseEnter={() => {
                handleMouseEnter(ins);
              }}
              onMouseLeave={handleMouseLeave}
            >
                <div
                  className={s.hexagon}
                  style={{ backgroundColor: hexagonColor }}
                />
            </div>
          </Bubble>
          );
        })}
        {instanceItems?.length > 100 && (
          <Bubble
            className="overNestBubble"
            arrowPointAtCenter
            placement="bottom"
            content={isTcHouse ? ClickHouseConfig.BASE_BUBBLE_TIP : t(
              '受展示限制，仅展示前100个资源，剩余{{count}}个资源已隐藏',
              { count: instanceItems.length - 100 }
            )}
          >
            <div className={s['hexagon-wrapper']}>
              <div className={s.hexagon}>
                <Icon type="more" />
              </div>
            </div>
          </Bubble>
        )}
      </div>
    );
  };

  const renderProgressBar = (metric: MetricItem) => (
    <div className={s['metric-row']}>
      <div className={s['metric-label']}>
        <p className={s['metric-text']}>
          <Tooltip title={metric.label}>
            {metric.label}
          </Tooltip>
        </p>
        <span
          className={s['metric-dot']}
          style={{ backgroundColor: metric.dotColor }}
        />
      </div>
      <div className={s['progress-container']}>
        <div className={s['progress-bar']}>
          <div
            className={s['progress-fill']}
            style={{
              '--progress-width': `${metric.value}%`,
              backgroundColor: metric.color,
            } as React.CSSProperties}
          />
        </div>
      </div>
      <div className={s['metric-value']}>{metric.value}%</div>
    </div>
  );

  return (
    <div className={`${s['resource-capacity-card']} ${className} ${baseMetrics.length <= sliceLength ? s['no-more'] : ''}`}>
      <div className={s.header}>
        <div className={s.icon}>
            <img src={resourceDistribute} />
        </div>
        <div className={s['title-section']}>
          <span className={s.title}>{title}</span>
          <span className={s['server-name']}>{data?.NodeName}</span>
        </div>
      </div>

      {renderHexagons()}

      <div
        className={s['metrics-section']}
        style={labelColumnWidthPx > 0 ? ({ '--metric-label-col-width': `${labelColumnWidthPx}px` } as React.CSSProperties) : undefined}
      >
        {displayedMetrics.length > 0 ? (
          displayedMetrics.map((metric, index) => (
            <div key={index}>
              {renderProgressBar(metric)}
            </div>
          ))
        ) : (
          <div className={s['no-data-container']}>
            <img className={s['no-data-image']} src={nodata} />
            <span className={s['no-data-text']}>{t('暂无数据')}</span>
            <span className={s['no-data-text']} style={{
              fontSize: 12,
              color: 'rgba(0, 0, 0, 0.4)',
            }}>{t('不支持此实例类型')}</span>
          </div>
        )}
      </div>

      {baseMetrics.length > sliceLength && (
        <div className={s.footer}>
          <span
            className={s['view-more']}
            onClick={handleToggleMetrics}
            style={{ cursor: 'pointer' }}
          >
            {showAllMetrics ? t('收起') : t('查看更多')}
          </span>
        </div>
      )}
    </div>
  );
};

export default ResourceCapacityCard;
