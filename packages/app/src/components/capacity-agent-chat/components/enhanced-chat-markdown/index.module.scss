.enhanced-chat-markdown {
  width: 100%;

  .mermaid<PERSON>lock {
    margin: 16px 0;

    .mermaid<PERSON><PERSON><PERSON> {
      display: block;
      width: 100%;
      max-width: 100%;
      overflow-x: auto;

      // Ensure SVG charts are responsive
      svg {
        max-width: 100%;
        height: auto;
      }
    }
  }

  .textBlock {
    // Ensure text blocks flow naturally
    display: block;
    width: 100%;

    // Remove default margins from ChatMarkdown to avoid spacing issues
    :global(.chat-markdown) {
      margin: 0;
      padding: 0;
    }
  }
}
