import React from 'react';
import {
  ChatMarkdown,
} from '@tencent/cloud-chat-ui';
import rehypeRaw from 'rehype-raw';
import MermaidRenderer from '../mermaid-renderer';
import { parseMarkdownWithMermaid, hasMermaidBlocks } from '../mermaid-renderer/utils';
import s from './index.module.scss';

interface EnhancedChatMarkdownProps {
  content: string;
  className?: string;
  markdownProps?: any;
}

const EnhancedChatMarkdown: React.FC<EnhancedChatMarkdownProps> = ({
  content,
  className = '',
  markdownProps = {},
}) => {
  const containsMermaid = hasMermaidBlocks(content);

  if (!containsMermaid) {
    return (
      <ChatMarkdown
        className={className}
        content={content}
        markdownProps={{
          rehypePlugins: [rehypeRaw as any],
          ...markdownProps,
        }}
      />
    );
  }

  const blocks = parseMarkdownWithMermaid(content);

  return (
    <div className={`enhanced-chat-markdown ${className}`}>
      {blocks.map((block, index) => {
        if (block.type === 'mermaid') {
          return (
            <div key={index} className={s.mermaidBlock}>
              <MermaidRenderer
                chart={block.content}
                className={s.mermaidRenderer}
              />
            </div>
          );
        }

        return (
            <div key={index} className={s.textBlock}>
              <ChatMarkdown
                content={block.content}
                markdownProps={{
                  rehypePlugins: [rehypeRaw as any],
                  ...markdownProps,
                }}
              />
            </div>
        );
      })}
    </div>
  );
};

export default EnhancedChatMarkdown;
