import React, { useState, useEffect } from 'react';
import { But<PERSON>, DatePicker as TeaDatePicker } from '@tencent/tea-component';
import moment from 'moment';
import { useHookstate } from '@hookstate/core';
import globalState from '@src/stores/global.state';
import checked from '@src/statics/svg/date-checked.svg';
import styles from './index.module.scss';
import { t } from '@tea/app/i18n';

const { RangePicker } = TeaDatePicker;
export interface DatePickerProps {
  /** 默认选中的时间范围 */
  defaultValue?: [moment.Moment, moment.Moment];
  /** 时间范围改变的回调 */
  onChange?: (dates: [moment.Moment, moment.Moment]) => void;
  /** 确认按钮的回调 */
  onConfirm?: (dates: [moment.Moment, moment.Moment]) => void;
  /** 取消按钮的回调 */
  onCancel?: () => void;
  /** 清空按钮的回调 */
  onClear?: () => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 发送消息的回调函数 */
  handleSendMessage?: (
    v: string,
    intentionParam?: object,
    intention?: string,
    isMutiThought?: boolean,
    options?: { addLast?: boolean; error?: string; }
  ) => void;
}

export interface PresetOption {
  label: string;
  value: string;
  getDates: () => [moment.Moment, moment.Moment];
}

const DatePicker: React.FC<DatePickerProps> = ({
  defaultValue,
  onChange,
  onConfirm,
  onCancel,
  onClear,
  disabled = false,
  handleSendMessage,
}) => {
  const global = useHookstate(globalState);
  // const { startDateTime, endDateTime } = global.agentDateRange.get();
  const [selectedPreset, setSelectedPreset] = useState<string>('');
  const [startDate, setStartDate] = useState<moment.Moment | null>(null);
  const [endDate, setEndDate] = useState<moment.Moment | null>(null);

  const [rangeDate, setRangeDate] = useState<[moment.Moment | null, moment.Moment | null]>([null, null]);
  const [showCustomInput, setShowCustomInput] = useState<boolean>(false);
  const [rangeInvalid, setRangeInvalid] = useState<boolean>(false);
  const [rangeError, setRangeError] = useState<string>('');

  const validateRange = (start: moment.Moment | null, end: moment.Moment | null) => {
    if (start && end) {
      const max = start.clone().add(1, 'month')
        .endOf('day');
      const invalid = end.isAfter(max);
      setRangeInvalid(invalid);
      setRangeError(invalid ? t('当前仅支持一个月内查询') : '');
    } else {
      setRangeInvalid(true);
      setRangeError(t('请选择时间'));
    }
  };

  // 预设时间选项
  const presetOptions: PresetOption[] = [
    {
      label: t('最近24小时'),
      value: 'last24h',
      getDates: () => {
        const end = moment();
        const start = moment().subtract(24, 'hours');
        return [start, end];
      },
    },
    {
      label: t('最近3天'),
      value: 'last3d',
      getDates: () => {
        const end = moment();
        const start = moment().subtract(3, 'days');
        return [start, end];
      },
    },
    {
      label: t('最近1周'),
      value: 'last1w',
      getDates: () => {
        const end = moment();
        const start = moment().subtract(1, 'week');
        return [start, end];
      },
    },
    {
      label: t('最近30天'),
      value: 'last30d',
      getDates: () => {
        const end = moment();
        const start = moment().subtract(30, 'days');
        return [start, end];
      },
    },
    {
      label: t('昨天'),
      value: 'yesterday',
      getDates: () => {
        const start = moment().subtract(1, 'day')
          .startOf('day');
        const end = moment().subtract(1, 'day')
          .endOf('day');
        return [start, end];
      },
    },
    {
      label: t('本周'),
      value: 'thisWeek',
      getDates: () => {
        const start = moment().startOf('week');
        const end = moment().endOf('week');
        return [start, end];
      },
    },
    {
      label: t('本月'),
      value: 'thisMonth',
      getDates: () => {
        const start = moment().startOf('month');
        const end = moment().endOf('month');
        return [start, end];
      },
    },
    {
      label: t('自定义'),
      value: 'custom',
      getDates: () => [startDate || moment(), endDate || moment()],
    },
  ];

  // 初始化默认值
  useEffect(() => {
    if (defaultValue) {
      setStartDate(defaultValue[0]);
      setEndDate(defaultValue[1]);
      validateRange(defaultValue[0], defaultValue[1]);
    }
  }, [defaultValue]);

  // 组件挂载时默认选中最近3天
  useEffect(() => {
    if (!defaultValue) {
      const end = moment();
      const start = moment().subtract(3, 'days');
      setStartDate(start);
      setEndDate(end);
      validateRange(start, end);
      setSelectedPreset('last3d');
      onChange?.([start, end]);
    }
  }, []);

  // 处理预设选项选择
  const handlePresetSelect = (preset: PresetOption) => {
    if (preset.value === 'custom') {
      setSelectedPreset('custom');
      setShowCustomInput(true);
      setStartDate(null);
      setEndDate(null);
      setRangeDate([null, null]);
      setRangeInvalid(true);
      setRangeError(t('请选择时间'));
      return;
    }

    const [start, end] = preset.getDates();
    setStartDate(start);
    setEndDate(end);
    validateRange(start, end);

    setSelectedPreset(preset.value);
    setShowCustomInput(false);
    onChange?.([start, end]);
  };

  // 处理确认
  const handleConfirm = () => {
    if (startDate && endDate) {
      global.set(state => ({
        ...state,
        agentDatePickerVisible: false,
        agentShowAnalysisResult: false,
        agentShowResourceCapacity: false,
        agentShowNotificationBanner: false,
        agentDateRange: {
          startDateTime: startDate,
          endDateTime: endDate,
        },
      }));
      onConfirm?.([startDate, endDate]);
      handleSendMessage?.('', {
        StartTime: startDate.format('YYYY-MM-DDTHH:mm:ssZ'),
        EndTime: endDate.format('YYYY-MM-DDTHH:mm:ssZ'),
      });
    }
  };

  // 处理清空
  const handleClear = () => {
    setStartDate(null);
    setEndDate(null);
    setSelectedPreset('');
    onClear?.();
    setShowCustomInput(false);
    setRangeDate([null, null]);
    setRangeInvalid(true);
    setRangeError(t('请选择时间'));
  };

  // 获取已选时间显示文本
  const getSelectedTimeText = () => {
    if (startDate && endDate) {
      return t('已选时间: {{attr0}} 至 {{attr1}}', { attr0: startDate.format(t('M月D日HH:mm')), attr1: endDate.format(t('M月D日HH:mm')) });
    }
    return '';
  };

  return (
    <div className={styles['date-picker']}>
      <div className={styles['date-picker-title']}>
        <span>{t('时长选择:')}</span>
        <Button
          type="link"
          disabled={disabled}
          onClick={handleClear}
          className={styles['date-picker-clear-btn']}
        >
          {t('清空')}
        </Button>
      </div>
      {/* 预设时间选项 */}
      <div className={styles['date-picker-presets']}>
        <div className={styles['date-picker-preset-row']}>
          {presetOptions.slice(0, 4).map(preset => (
            <div
              key={preset.value}
              onClick={() => handlePresetSelect(preset)}
              style={{
                backgroundImage: selectedPreset === preset.value ? `url(${checked})` : 'none',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: '62px 19px',
              }}
              className={`${styles['date-picker-preset-btn']} ${
                selectedPreset === preset.value ? styles['date-picker-preset-btn-active'] : ''
              }`}
            >
              {preset.label}
            </div>
          ))}
        </div>
        <div className={styles['date-picker-preset-row']}>
          {presetOptions.slice(4).map(preset => (
            <div
              key={preset.value}
              onClick={() => handlePresetSelect(preset)}
              style={{
                backgroundImage: selectedPreset === preset.value ? `url(${checked})` : 'none',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: '62px 19px',
              }}
              className={`${styles['date-picker-preset-btn']} ${
                selectedPreset === preset.value ? styles['date-picker-preset-btn-active'] : ''
              }`}
            >
              {preset.label}
            </div>
          ))}
        </div>
      </div>

      {/* 自定义时间输入 */}
      {showCustomInput && (
        <div className={styles['date-picker-custom']}>
          <RangePicker clearable className={styles['date-range-picker']} style={{ width: '100%', marginTop: 10 }} onChange={(v) => {
            setRangeDate(v);
            // 同时更新 startDate 和 endDate
            if (v && v[0] && v[1]) {
              setStartDate(v[0]);
              setEndDate(v[1]);
              validateRange(v[0], v[1]);
            } else {
              setStartDate(null);
              setEndDate(null);
              validateRange(null, null);
            }
          }} value={rangeDate} disabledDate={(current) => {
            if (!current) return false;
            // 禁用今天之后的日期
            const todayEnd = moment().endOf('day');
            if (current.isAfter(todayEnd)) return false;
            // 未选择或已完整选择，除未来日期外均可选
            return true;
          }} showTime={{
            defaultValue: [
              moment('00:00:00', 'HH:mm:ss'),
              moment('23:59:59', 'HH:mm:ss'),
            ],
          }} />
        </div>
      )}

      {/* 已选时间显示 */}
      {!showCustomInput && getSelectedTimeText() && (
        <div className={styles['date-picker-selected-time']}>
          {getSelectedTimeText()}
        </div>
      )}

      {rangeInvalid && (
        <div className={styles['date-picker-selected-time']} style={{ color: '#d54941', marginTop: 8, position: 'absolute' }}>
          {rangeError}
        </div>
      )}

      {/* 操作按钮 */}
      <div className={styles['date-picker-actions']}>
        <div style={{ marginLeft: 10 }}>
          <Button
            type="weak"
            disabled={disabled}
            className={styles['cancel-btn']}
            onClick={() => {
              globalState.set(state => ({
                ...state,
                agentDrawerVisible: false,
                agentRenderHoverCard: false,
                agentTimeLineBarVisible: false,
                agentTimeLineBarPlaying: false,
              }));
              onCancel?.();
            }}
          >
            {t('取消')}
          </Button>
          <Button
            type="primary"
            disabled={disabled || !startDate || !endDate || rangeInvalid}
            onClick={handleConfirm}
            style={{ marginLeft: 10 }}
          >
            {t('确认')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DatePicker;
