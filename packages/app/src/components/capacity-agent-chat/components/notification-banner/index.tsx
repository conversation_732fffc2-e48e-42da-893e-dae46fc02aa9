/**
 * @description 通知横幅组件
 */
import React from 'react';
import { Button } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
import globalState from '@src/stores/global.state';
import { useHookstate } from '@hookstate/core';
import s from './index.module.scss';

interface IProps {
  handleSendMessage?: (
    v: string,
    intentionParam?: object,
    intention?: string,
    isMutiThought?: boolean,
    options?: { addLast?: boolean; error?: string; }
  ) => void;
}
const NotificationBanner: React.FC<IProps> = ({
  handleSendMessage,
}: IProps) => {
  const agentCurrentTime = useHookstate(globalState.agentCurrentTime).value;
  const expandedHoverCardId = useHookstate(globalState.expandedHoverCardId).value;
  const handleConfirm = () => {
    handleSendMessage?.(
      '',
      {
        NodeUuid: expandedHoverCardId,
        NodeTimestamp: Math.floor(agentCurrentTime / 1000),
      },
      'InsightNode',
      true
    );

    // 点击确认后隐藏组件
    globalState.set(state => ({
      ...state,
      agentShowNotificationBanner: false,
    }));
  };

  return (
    <div className={s.container}>
      <div className={s.content}>
        <span className={s.text}>
          {t('容量回放助手已就绪：需要根因分析和智能建议？马上给你答案！')}
        </span>
        <div className={s['btn-wrapper']}>
            <Button
            type="primary"
            className={s['confirm-btn']}
            onClick={handleConfirm}
            >
            {t('确认')}
            </Button>
        </div>
      </div>
    </div>
  );
};

export default NotificationBanner;
