/**
 * @description 通知横幅组件
 */
import React from 'react';
import { t } from '@tea/app/i18n';
import { Bubble } from '@tencent/tea-component';
import timeIconSvg from '@src/statics/svg/time-icon.svg';
import globalState from '@src/stores/global.state';
import { useHookstate } from '@hookstate/core';
import s from './index.module.scss';

interface IProps {
  onConfirmBtnClick?: () => void;
  handleSendMessage?: (
    v: string,
    intentionParam?: object,
    intention?: string,
    isMutiThought?: boolean,
    options?: { addLast?: boolean; error?: string; }
  ) => void;
}
const RetryAnalysisCard: React.FC<IProps> = ({
  handleSendMessage,
  onConfirmBtnClick,
}: IProps) => {
  const agentCurrentTime = useHookstate(globalState.agentCurrentTime).value;
  const expandedHoverCardId = useHookstate(globalState.expandedHoverCardId).value;
  const handleConfirm = () => {
    onConfirmBtnClick?.();
    handleSendMessage?.(
      '',
      {
        NodeUuid: expandedHoverCardId,
        NodeTimestamp: Math.floor(agentCurrentTime / 1000),
      },
      'InsightNode',
      true
    );

    // 点击确认后隐藏组件
    globalState.set(state => ({
      ...state,
      agentShowRetryBanner: false,
    }));
  };

  return (
    <div className={s['retry-container']}>
      <Bubble
        arrowPointAtCenter
        placement="top"
        trigger="hover"
        content={t('时刻已切换，可基于当前时刻重新分析')}
      >
        <div className={s['retry-container-btn']} onClick={handleConfirm}>
        <img src={timeIconSvg} alt="" />
        <div>{t('重新分析')}</div>
      </div>
      </Bubble>
    </div>
  );
};

export default RetryAnalysisCard;
