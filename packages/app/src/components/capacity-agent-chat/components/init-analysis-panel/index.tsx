/* eslint-disable */
import React, { useEffect, useState } from 'react';
import rehypeRaw from 'rehype-raw';
import {
  AITrigger,
  ChatReasoning,
} from '@tencent/cloud-chat-ui';
import { Button } from 'tdesign-react';
import { StatusTextMap } from '@src/constants';
import { ChevronLeftIcon, ChevronRightIcon } from 'tdesign-icons-react';
import EnhancedChatMarkdown from '../enhanced-chat-markdown/index';
import {ReasoningStatus} from '../../index';
import s from './index.module.scss';
import { t } from '@tea/app/i18n';

const { DotLoading } = AITrigger;

interface DataAnalysisCardProps {
  processing: boolean;
  insightNodeStatus: any;
  mutiList: any[];
}

const InitAnalysisPanel: React.FC<DataAnalysisCardProps> = ({
  processing,
  mutiList,
  insightNodeStatus,
}) => {
  const [pageCount, setPageCount] = useState(1);
  const [isExpanded, setIsExpanded] = useState(true);
  const [count, setCount] = useState(0);
  const [reasoningStatus, setReasoningStatus] = useState(ReasoningStatus.finished);
  const content = mutiList?.[pageCount - 1]?.content;
  const thought = mutiList?.[pageCount - 1]?.thought;

  useEffect(() => {
    if (!content && thought) {
      setReasoningStatus(ReasoningStatus.loading);
      setIsExpanded(true);
    }
    if (content) {
      setReasoningStatus(ReasoningStatus.finished);
      setIsExpanded(false);
    }

  }, [content, thought]);

  useEffect(() => {
    let timer;
    if (reasoningStatus === ReasoningStatus.loading && processing) {
      timer = setInterval(() => {
        setCount(prev => prev + 1);
      }, 1000);
    }
    if (reasoningStatus === ReasoningStatus.loading && !processing) {
      setReasoningStatus(ReasoningStatus.stopped);
    }
    return () => timer && clearInterval(timer);
  }, [reasoningStatus, processing]);

  // 当下一项中thought有值时，自动翻到下一个页
  useEffect(() => {
    const nextItem = mutiList?.[pageCount];
    if (nextItem?.thought && pageCount < mutiList.length) {
      setPageCount(pageCount + 1);
    }
  }, [mutiList, pageCount]);

  return (
    <div className={s['content-wrapper']}>
      {/* {stepContent?.IsHealthy && <img src={greenSuccessSvg} />} */}
      <ChatReasoning
        isExpanded={isExpanded}
        statusIcon={reasoningStatus}
        statusText={StatusTextMap[reasoningStatus as keyof typeof StatusTextMap] || t('思考中...')}
        time={`${count}S`}
      >
        <EnhancedChatMarkdown content={thought} />
      </ChatReasoning>
      <EnhancedChatMarkdown
        className={s['analysis-data-card-content']}
        content={content}
        markdownProps={{
          rehypePlugins: [rehypeRaw as any],
        }}
      />
      {processing && content && !mutiList[pageCount]?.thought && <DotLoading />}
      {
        mutiList?.length > 1
        && <div className={s['analysis-data-card-content-page']}>
          <Button
            shape="circle"
            variant="text"
            disabled={pageCount === 1}
            onClick={() => setPageCount(pageCount - 1)}
          >
            <ChevronLeftIcon />
          </Button>
          <span>{`${pageCount}/${mutiList?.length}`}</span>
          <Button
            shape="circle"
            variant="text"
            disabled={pageCount === mutiList?.length}
            onClick={() => setPageCount(pageCount + 1)}
          >
            <ChevronRightIcon />
          </Button>
        </div>
      }
    </div>
  );
};

export default InitAnalysisPanel;
