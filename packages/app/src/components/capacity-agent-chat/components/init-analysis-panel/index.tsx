import React, { useEffect, useState, useRef } from 'react';
import rehypeRaw from 'rehype-raw';
import {
  AITrigger,
  ChatReasoning,
} from '@tencent/cloud-chat-ui';
import { Button } from 'tdesign-react';
import { StatusTextMap } from '@src/constants';
import greenSuccessSvg from '@src/statics/svg/green-success.svg';
import { ChevronLeftIcon, ChevronRightIcon } from 'tdesign-icons-react';
import EnhancedChatMarkdown from '../enhanced-chat-markdown/index';
import { ReasoningStatus } from '../../index';
import s from './index.module.scss';
import { t } from '@tea/app/i18n';

const { DotLoading } = AITrigger;

interface DataAnalysisCardProps {
  processing: boolean;
  stepContent: any;
  mutiList: any[];
}

const InitAnalysisPanel: React.FC<DataAnalysisCardProps> = ({
  processing,
  mutiList,
  stepContent,
}) => {
  const [pageCount, setPageCount] = useState(1);
  const [isExpanded, setIsExpanded] = useState(true);
  const [reasoningStatus, setReasoningStatus] = useState(ReasoningStatus.finished);

  // 使用useRef保存每个项目的计时器和时间，避免抽屉关闭时清零
  const timersRef = useRef<{ [key: number]: NodeJS.Timeout | null }>({});
  const countsRef = useRef<{ [key: number]: number }>({});
  const [, forceUpdate] = useState({});

  const currentIndex = pageCount - 1;
  const rawContent = mutiList?.[currentIndex]?.content;
  const thought = mutiList?.[currentIndex]?.thought;
  const currentCount = countsRef.current[currentIndex] || 0;

  // 处理content：删除特定标记
  const content = rawContent ? (() => {
    let processedContent = rawContent;

    // 匹配到$[初步诊断]，删除这个字符串
    processedContent = processedContent.replace(/\$\[初步诊断\]/g, '');

    // 匹配到$[根因分析]，删除这个字符串及其后面的所有内容
    const rootCauseIndex = processedContent.indexOf(t('$[根因分析]'));
    if (rootCauseIndex !== -1) {
      processedContent = processedContent.substring(0, rootCauseIndex);
    }

    return processedContent;
  })() : rawContent;

  useEffect(() => {
    if (!content && thought) {
      setReasoningStatus(ReasoningStatus.loading);
      setIsExpanded(true);
    }
    if (content) {
      setReasoningStatus(ReasoningStatus.finished);
      setIsExpanded(false);
    }
  }, [content, thought]);

  useEffect(() => {
    const currentIndex = pageCount - 1;

    // 初始化当前项目的计数器
    if (countsRef.current[currentIndex] === undefined) {
      countsRef.current[currentIndex] = 0;
    }

    // 清除之前的计时器
    if (timersRef.current[currentIndex]) {
      clearInterval(timersRef.current[currentIndex]!);
      timersRef.current[currentIndex] = null;
    }

    if (reasoningStatus === ReasoningStatus.loading && processing) {
      timersRef.current[currentIndex] = setInterval(() => {
        countsRef.current[currentIndex] = (countsRef.current[currentIndex] || 0) + 1;
        forceUpdate({}); // 强制更新组件以显示新的时间
      }, 1000);
    }

    if (reasoningStatus === ReasoningStatus.loading && !processing) {
      setReasoningStatus(ReasoningStatus.stopped);
    }

    return () => {
      if (timersRef.current[currentIndex]) {
        clearInterval(timersRef.current[currentIndex]!);
        timersRef.current[currentIndex] = null;
      }
    };
  }, [reasoningStatus, processing, pageCount]);

  // 当下一项中thought有值时，自动翻到下一个页
  useEffect(() => {
    const nextItem = mutiList?.[pageCount];
    if (nextItem?.thought && pageCount < mutiList.length && processing) {
      setPageCount(pageCount + 1);
    }
  }, [mutiList, pageCount, processing]);

  return (
    <div className={s['content-wrapper']}>
      <ChatReasoning
        isExpanded={isExpanded}
        statusIcon={reasoningStatus}
        statusText={StatusTextMap[reasoningStatus as keyof typeof StatusTextMap] || t('思考中...')}
        time={`${currentCount}S`}
      >
        <EnhancedChatMarkdown content={thought} />
      </ChatReasoning>
      <div className={s['content-wrapper-content']}>
        {stepContent?.IsHealthy && content && <img src={greenSuccessSvg} />}
        <EnhancedChatMarkdown
          className={s['analysis-data-card-content']}
          content={content}
          markdownProps={{
            rehypePlugins: [rehypeRaw as any],
          }}
        />
      </div>

      {processing && content && !mutiList[pageCount]?.thought && <DotLoading />}
      {
        mutiList?.length > 1
        && <div className={s['analysis-data-card-content-page']}>
          <Button
            shape="circle"
            variant="text"
            disabled={pageCount === 1 || processing}
            onClick={() => setPageCount(pageCount - 1)}
          >
            <ChevronLeftIcon />
          </Button>
          <span>{`${pageCount}/${mutiList?.length}`}</span>
          <Button
            shape="circle"
            variant="text"
            disabled={pageCount === mutiList?.length || processing}
            onClick={() => setPageCount(pageCount + 1)}
          >
            <ChevronRightIcon />
          </Button>
        </div>
      }
    </div>
  );
};

export default InitAnalysisPanel;
