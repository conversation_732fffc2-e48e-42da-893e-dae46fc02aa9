import React, { useEffect, useState, useMemo } from 'react';
import {
  ChatContent,
  ChatItem,
  ChatReasoning,
} from '@tencent/cloud-chat-ui';
import EnhancedChatMarkdown from '../enhanced-chat-markdown/index';
import globalState from '@src/stores/global.state';
import { useHookstate } from '@hookstate/core';
import { t } from '@tea/app/i18n';
import { find, isEmpty } from 'lodash';
import { GET_MONITOR_DATA_RESULT, GET_MONITOR_DATA, INSIGHT_NODE, StatusTextMap } from '@src/constants';
import AnalysisResultCard from '../analysis-result-card';
import ResourceCapacityCard from '../resource-capacity-card';
import HoverCardClickTip from '../hover-card-click-tip';
import NotificationBanner from '../notification-banner';
// import DatePicker from '../date-picker';
import s from './index.module.scss';
import DataAnalysisCard from '../data-analysis-card';
import { ContentType, MessageType, MessageTypeEnum, ReasoningStatus } from '../../index';

interface MessageItemProps {
  item: MessageType;
  processing?: boolean;
  handleSendMessage?: (
    v: string,
    intentionParam?: object,
    intention?: string,
    isMutiThought?: boolean,
    options?: {
    addLast?: boolean;
    error?: string;
  }) => void;
  costTime: number;
  reasoningStatus: ReasoningStatus;
}

// 常量提取到组件外部，避免重复创建
const FIRST_THINKING_STEPS = [GET_MONITOR_DATA_RESULT, GET_MONITOR_DATA];

// 单个消息组件
const MessageItem = (props: MessageItemProps) => {
  const {
    item,
    processing,
    handleSendMessage,
    costTime,
    reasoningStatus,
  } = props;
  const global = useHookstate(globalState);
  const isUser = item.type === MessageTypeEnum.user;
  const { steps, deepThink, content, mutiList } = item;
  const [isExpanded, setIsExpanded] = useState(true);
  const insightNodeStatus = find(item?.steps, item => (
    item.stepKey === INSIGHT_NODE
  )) || {};

  const isFirstThinking = useMemo(() => {
    if (!steps || !Array.isArray(steps) || steps.length === 0) {
      return false;
    }

    // 检查是否所有步骤都是首次思考步骤
    return steps.every(step => step?.stepKey && FIRST_THINKING_STEPS.includes(step.stepKey));
  }, [steps]);

  // 优化全局状态获取，避免重复调用
  const agentShowAnalysisResult = useMemo(() => global.agentShowAnalysisResult.get(), [global.agentShowAnalysisResult]);
  const agentShowResourceCapacity = useMemo(() => global.agentShowResourceCapacity.get(), [global.agentShowResourceCapacity]);
  const agentShowNotificationBanner = useMemo(() => global.agentShowNotificationBanner.get(), [global.agentShowNotificationBanner]);

  useEffect(() => {
    if (!content && (deepThink || (steps && steps.length > 0))) {
      setIsExpanded(true);
    }
    if (content) {
      setIsExpanded(false);
    }
  }, [content, steps, deepThink]);

  const renderContent = (item: MessageType) => {
    switch (item.contentType) {
      case ContentType.text: {
        if (isUser) {
          return <ChatItem role='user' className={s['message-user-item']}>
            <ChatContent content={item.content} type='text'/>
          </ChatItem>;
        }
        return (
          <>
            {
              deepThink
              && <ChatReasoning
                isExpanded={isExpanded}
                statusIcon={reasoningStatus}
                statusText={StatusTextMap[reasoningStatus as keyof typeof StatusTextMap] || t('思考中...')}
                time={`${costTime}S`}
              >
                <EnhancedChatMarkdown content={deepThink} />
              </ChatReasoning>
            }
            {
              content
              && <ChatItem
                role='assistant'
                className={s['message-assistant-item']}
              >
                <ChatContent content={content} type='text' />
              </ChatItem>
            }
            {
              !isEmpty(mutiList)
              && <DataAnalysisCard
                insightNodeStatus={insightNodeStatus}
                processing={processing}
                mutiList={mutiList}
              />
            }
          </>
        );
      }
      case ContentType.default:
        return <div>
          {item?.content?.message ?? t('正在处理中...')}
          </div>;
      default:
        return null;
    }
  };

  return <>
    {renderContent(item)}
    {isFirstThinking && (
      <div style={{ width: '100%' }}>
        {agentShowAnalysisResult && <AnalysisResultCard />}
        {agentShowAnalysisResult && !agentShowResourceCapacity && <HoverCardClickTip />}
        {agentShowResourceCapacity && <ResourceCapacityCard />}
        {agentShowNotificationBanner && <NotificationBanner
          handleSendMessage={handleSendMessage}
         />}
      </div>
    )}
  </>;
};

export default MessageItem;
