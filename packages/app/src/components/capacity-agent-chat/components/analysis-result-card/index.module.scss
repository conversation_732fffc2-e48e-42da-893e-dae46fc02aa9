.analysis-result-card {
  background: white;
  border-radius: 5px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  align-items: center;
  gap: 5px;
}

.warning-icon {
  width: 20px;
  height: 20px;
  background: #FF6B35;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  flex-shrink: 0;
}

.title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.risk-period-row {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:last-child {
    border-bottom: none;
  }
}

.time-range {
  color: #6B7280;
  font-weight: 400;
  flex: 1;
  margin-right: 15px;
  color: var(--Brand-Brand6-Hover, #366EF4);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  min-width: 185px;
  &:hover {
    cursor: pointer;
  }
}

.description {
  font-size: 14px;
  font-weight: 500;
  text-align: right;
  flex-shrink: 0;
  color: #000;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  width: 100%;
  flex: 1;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}