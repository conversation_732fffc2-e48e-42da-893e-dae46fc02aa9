/* eslint-disable */
import React, { useEffect, useState } from 'react';
import rehypeRaw from 'rehype-raw';
import {
  AITrigger,
  ChatReasoning,
} from '@tencent/cloud-chat-ui';
import { Button } from 'tdesign-react';
import { StatusTextMap } from '@src/constants';
import { ChevronLeftIcon, ChevronRightIcon } from 'tdesign-icons-react';
import dataAnalysisSvg from '@src/statics/svg/mathematics.svg';
import baseAnalysisSvg from '@src/statics/svg/chart-bubble.svg';
import greenSuccessSvg from '@src/statics/svg/green-success.svg';
import adviceSvg from '@src/statics/svg/questionnaire-double.svg';
import InitAnalysisPanel from '../init-analysis-panel';
import EnhancedChatMarkdown from '../enhanced-chat-markdown/index';
import s from './index.module.scss';
import { t } from '@tea/app/i18n';

const { DotLoading } = AITrigger;

interface DataAnalysisCardProps {
  processing: boolean;
  insightNodeStatus: any;
  mutiList: any[];
}

const DataAnalysisCard: React.FC<DataAnalysisCardProps> = ({
  processing,
  mutiList,
  insightNodeStatus,
}) => {
  const { stepContent } = insightNodeStatus;
  const contentResult = mutiList.map(v => v.content).join('');

  const [hasAnalysisPattern, setHasAnalysisPattern] = useState(false);
  const [hasAdvicePattern, setHasAdvicePattern] = useState(false);
  const [analysisContent, setAnalysisContent] = useState('');
  const [adviceContent, setAdviceContent] = useState('');
  const [pageCount, setPageCount] = useState(1);

  const [pureDataAnalysisContent, setPureDataAnalysisContent] = useState(''); // 纯净的初步诊断内容，不含任何$[]标签

  // 流式输出相关状态
  const [displayedAnalysisContent, setDisplayedAnalysisContent] = useState('');
  const [displayedAdviceContent, setDisplayedAdviceContent] = useState('');
  const [isAnalysisStreaming, setIsAnalysisStreaming] = useState(false);
  const [isAdviceStreaming, setIsAdviceStreaming] = useState(false);
  const [showAnalysisCard, setShowAnalysisCard] = useState(false);
  const [showAdviceCard, setShowAdviceCard] = useState(false);
  const [analysisCompleted, setAnalysisCompleted] = useState(false);

  useEffect(() => {
    // 处理主要内容
    let contentWithoutMarkers = contentResult;

    // 处理纯净的初步诊断内容（始终不含任何$[]标签）
    // 在processing阶段，显示所有内容但去除$[]标签
    // 在完成阶段，只显示主要分析内容，不包含根因分析和优化建议的内容
    let pureContent = contentResult;

    if (!processing) {
      // 完成阶段：移除最后的根因分析和优化建议内容，只保留主要分析部分
      const firstSpecialIndex = Math.min(
        contentResult.indexOf(t('$[根因分析]')) !== -1 ? contentResult.indexOf(t('$[根因分析]')) : Infinity,
        contentResult.indexOf(t('$[优化建议]')) !== -1 ? contentResult.indexOf(t('$[优化建议]')) : Infinity
      );

      if (firstSpecialIndex !== Infinity) {
        pureContent = contentResult.substring(0, firstSpecialIndex);
      }
    }

    // 移除所有$[]标签
    pureContent = pureContent.replace(/\$\[.*?\]/g, '');
    setPureDataAnalysisContent(pureContent.trim());

    if (processing) {
      // processing为true时，不清除$[根因分析]和$[优化建议]标签
      // 只清除$[初步诊断]等其他标签
      contentWithoutMarkers = contentResult.replace(/\$\[(?!根因分析|优化建议).*?\]/g, '');
    } else {
      // processing为false时，处理完整的contentResult进行匹配
      const analysisPattern = /\$\[根因分析\]/g;
      const advicePattern = /\$\[优化建议\]/g;

      const hasAnalysis = analysisPattern.test(contentResult || '');
      const hasAdvice = advicePattern.test(contentResult || '');

      // 设置板块状态
      setHasAnalysisPattern(hasAnalysis);
      setHasAdvicePattern(hasAdvice);

      // 重置流式输出状态
      setShowAnalysisCard(false);
      setShowAdviceCard(false);
      setDisplayedAnalysisContent('');
      setDisplayedAdviceContent('');
      setAnalysisCompleted(false);

      // 处理内容，只清除最后一个匹配的标签和内容
      contentWithoutMarkers = contentResult;

      // 提取根因分析的最后一个内容
      if (hasAnalysis) {
        // 找到所有$[根因分析]的匹配位置
        const analysisMatches = [...contentResult.matchAll(/\$\[根因分析\]/g)];
        if (analysisMatches.length > 0) {
          // 获取最后一个$[根因分析]的位置
          const lastAnalysisMatch = analysisMatches[analysisMatches.length - 1];
          const lastAnalysisIndex = lastAnalysisMatch.index;

          // 提取最后一个$[根因分析]的内容
          const contentFromLastAnalysis = contentResult.substring(lastAnalysisIndex);
          const analysisContentMatch = contentFromLastAnalysis.match(/\$\[根因分析\]([\s\S]*?)(?=\$\[|$)/);
          if (analysisContentMatch && analysisContentMatch[1]) {
            setAnalysisContent(analysisContentMatch[1].trim());

            // 从主内容中移除最后一个$[根因分析]及其内容
            const endIndex = lastAnalysisIndex + analysisContentMatch[0].length;
            contentWithoutMarkers = contentResult.substring(0, lastAnalysisIndex) + contentResult.substring(endIndex);
          }
        }
      }

      // 提取优化建议的最后一个内容
      if (hasAdvice) {
        // 找到所有$[优化建议]的匹配位置
        const adviceMatches = [...contentWithoutMarkers.matchAll(/\$\[优化建议\]/g)];
        if (adviceMatches.length > 0) {
          // 获取最后一个$[优化建议]的位置
          const lastAdviceMatch = adviceMatches[adviceMatches.length - 1];
          const lastAdviceIndex = lastAdviceMatch.index;

          // 提取最后一个$[优化建议]的内容
          const contentFromLastAdvice = contentWithoutMarkers.substring(lastAdviceIndex);
          const adviceContentMatch = contentFromLastAdvice.match(/\$\[优化建议\]([\s\S]*?)(?=\$\[|$)/);
          if (adviceContentMatch && adviceContentMatch[1]) {
            setAdviceContent(adviceContentMatch[1].trim());

            // 从主内容中移除最后一个$[优化建议]及其内容
            const endIndex = lastAdviceIndex + adviceContentMatch[0].length;
            contentWithoutMarkers = contentWithoutMarkers.substring(0, lastAdviceIndex) + contentWithoutMarkers.substring(endIndex);
          }
        }
      }

      // 清除其他$[]标记（除了保留的$[根因分析]和$[优化建议]）
      contentWithoutMarkers = contentWithoutMarkers.replace(/\$\[(?!根因分析|优化建议).*?\]/g, '');
    }
  }, [contentResult, processing]);

  // 流式输出控制 useEffect - 启动根因分析
  useEffect(() => {
    if (!processing && hasAnalysisPattern && analysisContent && !showAnalysisCard && !analysisCompleted) {
      // 延迟显示根因分析卡片，然后开始流式输出
      setTimeout(() => {
        setShowAnalysisCard(true);
        startAnalysisStreaming();
      }, 300);
    }
  }, [processing, hasAnalysisPattern, analysisContent, showAnalysisCard, analysisCompleted]);

  // 监听根因分析完成，启动优化建议
  useEffect(() => {
    if (!processing && hasAdvicePattern && adviceContent && analysisCompleted && !showAdviceCard) {
      // 根因分析完成后，延迟显示优化建议卡片
      setTimeout(() => {
        setShowAdviceCard(true);
        startAdviceStreaming();
      }, 500);
    }
  }, [processing, hasAdvicePattern, adviceContent, analysisCompleted, showAdviceCard]);

  // 处理只有优化建议没有根因分析的情况
  useEffect(() => {
    if (!processing && hasAdvicePattern && adviceContent && !hasAnalysisPattern && !showAdviceCard) {
      // 没有根因分析时，直接显示优化建议
      setTimeout(() => {
        setShowAdviceCard(true);
        startAdviceStreaming();
      }, 300);
    }
  }, [processing, hasAdvicePattern, adviceContent, hasAnalysisPattern, showAdviceCard]);

  // 流式输出函数
  const startAnalysisStreaming = () => {
    if (!analysisContent) return;

    setIsAnalysisStreaming(true);
    setDisplayedAnalysisContent('');
    let index = 0;

    const timer = setInterval(() => {
      if (index < analysisContent.length) {
        setDisplayedAnalysisContent(analysisContent.slice(0, index + 1));
        index++;
      } else {
        setIsAnalysisStreaming(false);
        setAnalysisCompleted(true); // 标记根因分析完成
        clearInterval(timer);
      }
    }, 10); // 10ms 间隔，可以调整速度
  };

  const startAdviceStreaming = () => {
    if (!adviceContent) return;

    setIsAdviceStreaming(true);
    setDisplayedAdviceContent('');
    let index = 0;

    const timer = setInterval(() => {
      if (index < adviceContent.length) {
        setDisplayedAdviceContent(adviceContent.slice(0, index + 1));
        index++;
      } else {
        setIsAdviceStreaming(false);
        clearInterval(timer);
      }
    }, 10); // 10ms 间隔，可以调整速度
  };

  return (
    <div>
      <div className={s['analysis-data-card']}>
        <div className={s['analysis-data-card-title']}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <img src={dataAnalysisSvg} />
            <span>{t('初步诊断')}</span>
          </div>
        </div>
        <InitAnalysisPanel
          mutiList={mutiList}
          processing={processing}
          insightNodeStatus={insightNodeStatus}
        />
      </div>
      {
        showAnalysisCard && hasAnalysisPattern
        && <div className={s['analysis-data-card']}>
          <div className={s['analysis-data-card-title']}>
            <img src={baseAnalysisSvg} />
            <span>{t('根因分析')}</span>
          </div>
          <EnhancedChatMarkdown
            className={s['analysis-data-card-content']}
            content={displayedAnalysisContent}
            markdownProps={{
              rehypePlugins: [rehypeRaw as any],
            }}
          />
          {isAnalysisStreaming && <DotLoading />}
        </div>
      }
      {
        showAdviceCard && hasAdvicePattern
        && <div className={s['analysis-data-card']}>
          <div className={s['analysis-data-card-title']}>
            <img src={adviceSvg} />
            <span>{t('优化建议')}</span>
          </div>
          <EnhancedChatMarkdown
            className={s['analysis-data-card-content']}
            content={displayedAdviceContent}
            markdownProps={{
              rehypePlugins: [rehypeRaw as any],
            }}
          />
          {isAdviceStreaming && <DotLoading />}
        </div>
      }
    </div>

  );
};

export default DataAnalysisCard;
