import React, { useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';
import { t } from '@tea/app/i18n';

interface MermaidRendererProps {
  chart: string;
  className?: string;
}

const MermaidRenderer: React.FC<MermaidRendererProps> = ({ chart, className = '' }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [isRendered, setIsRendered] = useState(false);

  useEffect(() => {
    // 使用配置初始化 Mermaid
    mermaid.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
      fontFamily: 'Arial, sans-serif',
      fontSize: 14,
      themeVariables: {
        primaryColor: '#1890ff',
        primaryTextColor: '#333',
        primaryBorderColor: '#1890ff',
        lineColor: '#666',
        secondaryColor: '#f0f0f0',
        tertiaryColor: '#e6f7ff',
      },
    });
  }, []);

  useEffect(() => {
    if (!containerRef.current || !chart || isRendered) return;

    const renderChart = async () => {
      try {
        setError(null);

        if (containerRef.current) {
          containerRef.current.innerHTML = '';
        }

        const chartId = `mermaid-${Date.now()}-${Math.random().toString(36)
          .substr(2, 9)}`; // 生成唯一图表 ID

        const tempContainer = document.createElement('div'); // 临时容器用于渲染
        tempContainer.id = chartId;
        tempContainer.className = 'mermaid';
        tempContainer.textContent = chart;

        if (containerRef.current) {
          containerRef.current.appendChild(tempContainer);
        }

        const svg = await mermaid.render(chartId, chart); // 渲染为 SVG
        if (containerRef.current) {
          containerRef.current.innerHTML = svg;
        }
        setIsRendered(true);
      } catch (err) {
        console.error(t('Mermaid 渲染错误'), err);
        setError(err instanceof Error ? err.message : t('图表渲染失败'));
        setIsRendered(false);
      }
    };

    renderChart();
  }, [chart, isRendered]);

  useEffect(() => {
    setIsRendered(false);
  }, [chart]);

  if (error) {
    return (
      <div className={`mermaid-error ${className}`} style={{
        padding: '16px',
        border: '1px solid #ff4d4f',
        borderRadius: '6px',
        backgroundColor: '#fff2f0',
        color: '#cf1322',
        fontSize: '14px',
      }}>
        <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{t('图表渲染失败')}</div>
        <div>{error}</div>
        <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
          {t('原始代码')}: <code style={{ backgroundColor: '#f5f5f5', padding: '2px 4px', borderRadius: '3px' }}>{chart}</code>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`mermaid-container ${className}`}
      style={{
        textAlign: 'center',
        padding: '16px',
        backgroundColor: '#fafafa',
        borderRadius: '6px',
        border: '1px solid #e8e8e8',
      }}
    />
  );
};

export default MermaidRenderer;
