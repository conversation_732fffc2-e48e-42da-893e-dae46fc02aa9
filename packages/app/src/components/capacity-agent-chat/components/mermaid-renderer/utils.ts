export interface MermaidBlock {
  type: 'mermaid';
  content: string;
  startIndex: number;
  endIndex: number;
}

export interface TextBlock {
  type: 'text';
  content: string;
  startIndex: number;
  endIndex: number;
}

export type ContentBlock = MermaidBlock | TextBlock;

/**
 * 解析 Markdown 文本并提取 Mermaid 代码块
 * @param content - 待解析的 Markdown 文本
 * @returns 文本与 Mermaid 代码块组成的区块数组
 */
export function parseMarkdownWithMermaid(content: string): ContentBlock[] {
  const blocks: ContentBlock[] = [];
  let currentIndex = 0;

  // 匹配 Mermaid 代码块的正则
  // 匹配形如 ```mermaid ... ``` 的代码块
  const mermaidRegex = /```mermaid\s*([\s\S]*?)```/g;

  let match;
  while ((match = mermaidRegex.exec(content)) !== null) {
    const fullMatch = match[0];
    const mermaidContent = match[1];
    const startIndex = match.index;
    const endIndex = startIndex + fullMatch.length;

    // 将 Mermaid 代码块之前的普通文本加入结果
    if (startIndex > currentIndex) {
      const textContent = content.substring(currentIndex, startIndex);
      if (textContent.trim()) {
        blocks.push({
          type: 'text',
          content: textContent,
          startIndex: currentIndex,
          endIndex: startIndex
        });
      }
    }

    // 将 Mermaid 代码块加入结果
    blocks.push({
      type: 'mermaid',
      content: mermaidContent.trim(),
      startIndex,
      endIndex,
    });

    currentIndex = endIndex;
  }

  // 追加最后一个 Mermaid 代码块之后剩余的文本
  if (currentIndex < content.length) {
    const remainingText = content.substring(currentIndex);
    if (remainingText.trim()) {
      blocks.push({
        type: 'text',
        content: remainingText,
        startIndex: currentIndex,
        endIndex: content.length,
      });
    }
  }

  return blocks;
}

/**
 * 判断内容中是否包含 Mermaid 代码块
 * @param content - 待检查内容
 * @returns 如果包含 Mermaid 代码块则返回 true
 */
export function hasMermaidBlocks(content: string): boolean {
  return /```mermaid\s*[\s\S]*?```/g.test(content);
}

/**
 * 仅提取内容中的 Mermaid 代码块
 * @param content - 待提取内容
 * @returns Mermaid 代码字符串数组
 */
export function extractMermaidBlocks(content: string): string[] {
  const blocks: string[] = [];
  const mermaidRegex = /```mermaid\s*([\s\S]*?)```/g;

  let match;
  while ((match = mermaidRegex.exec(content)) !== null) {
    blocks.push(match[1].trim());
  }

  return blocks;
}
