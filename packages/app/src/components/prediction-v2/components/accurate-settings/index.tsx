import React from 'react';
import { <PERSON>lapse, Select, Button, Icon, Bubble, InputNumber } from '@tencent/tea-component';
import { Popconfirm } from 'tdesign-react';
import { t } from '@tea/app/i18n';
import info from '@src/statics/svg/info.svg';
import globalState from '@src/stores/global.state';
import s from './index.module.scss';

interface IProps {
  accurateSettings: any[];
  setAccurateSettings: (settings: any[]) => void;
  defaultActiveIds: string[];
  periodTypeInfosOpts: Array<{ value: any; text: string }>;
  algorithmInfosOpts: Array<{ value: any; text: string }>;
  algorithmTypeMap: Record<string, string>;
  dialogVisible: boolean;
  setDialogVisible: (visible: boolean) => void;
  onDelete: (product: any, prediction: any) => void;
  arInfo: any;
  products: string[];
  archInfo: any;
}

/**
 * 精确设置组件
 * @param {IProps} props - 组件属性
 * @returns React.ReactElement
 */
export default function AccurateSettings(props: IProps): React.ReactElement {
  const {
    accurateSettings,
    setAccurateSettings,
    defaultActiveIds,
    periodTypeInfosOpts,
    algorithmInfosOpts,
    algorithmTypeMap,
    dialogVisible,
    setDialogVisible,
    onDelete,
    arInfo,
    products,
    archInfo,
  } = props;

  return (
    <div className={s.block}>
      <p className={s.header}>{t('精确设置')}</p>
      <div style={{ marginTop: 10 }}>
        <Collapse
          defaultActiveIds={defaultActiveIds}
          key={JSON.stringify(defaultActiveIds)}
          className={s.collapse}
        >
          {(accurateSettings || []).map((v, index) => (
            <Collapse.Panel
              id={`${index}`}
              title={v?.ProductName || '-'}
              key={`PredictionNodeInfos-${index}`}
            >
              {(v?.PredictionNodeInfos || []).map((vv, i) => (
                <div
                  className={s.panel}
                  key={`PredictionNodeInfos-${index}-${i}-${
                    vv?.NodeId || vv?.NodeUuid
                  }`}
                >
                  <div className={s.row}>
                    <p>{t('图元名称')}</p>
                    <p style={{ color: '#000' }} className={s.photoName}>
                      {vv?.ItemName}
                    </p>
                    <Popconfirm
                      content={t('确认删除吗')}
                      cancelBtn={null}
                      onConfirm={() => onDelete(v, vv)}
                      placement="left"
                    >
                      <Icon
                        type="delete"
                        className={s.deleteIcon}
                        onClick={() => {
                          // eslint-disable-next-line no-param-reassign
                        }}
                      />
                    </Popconfirm>
                  </div>
                  <div className={s.row}>
                    <p>{t('参考周期')}</p>
                    <Select
                      matchButtonWidth
                      appearance="button"
                      style={{ width: 110 }}
                      options={periodTypeInfosOpts}
                      value={vv?.PeriodValue}
                      onChange={(value) => {
                        // eslint-disable-next-line no-param-reassign
                        vv.PeriodValue = value;
                        setAccurateSettings([...accurateSettings]);
                      }}
                      placeholder={t('请选择')}
                    />
                  </div>
                  <div className={s.row}>
                    <p>{t('计算方法')}</p>
                    <Select
                      appearance="button"
                      matchButtonWidth
                      value={vv.AlgorithmType}
                      style={{ width: 110 }}
                      options={algorithmInfosOpts}
                      onChange={(value) => {
                        // eslint-disable-next-line no-param-reassign
                        vv.AlgorithmType = value;
                        setAccurateSettings([...accurateSettings]);
                      }}
                      placeholder={t('请选择')}
                    />
                    {algorithmTypeMap[vv.AlgorithmType] ? (
                      <Bubble
                        placement="top"
                        trigger="hover"
                        dark
                        content={algorithmTypeMap[vv.AlgorithmType]}
                      >
                        <img
                          style={{ marginLeft: 15 }}
                          src={info}
                          alt=""
                          className={s.info}
                        />
                      </Bubble>
                    ) : null}
                  </div>
                  <div className={s.row}>
                    <p>{t('放量倍数')}</p>
                    <InputNumber
                      hideButton
                      style={{ width: 110 }}
                      onFocus={() => {
                        arInfo.addNodeClass(products, s.grey);
                        arInfo.removeNodeClass(vv.NodeUuid, s.grey);
                      }}
                      onBlur={() => {
                        archInfo.removeAllNodeClass();
                      }}
                      value={vv.ResourceTimes}
                      onChange={(value) => {
                        // eslint-disable-next-line no-param-reassign
                        vv.ResourceTimes = value;
                        setAccurateSettings([...accurateSettings]);
                      }}
                      min={1}
                      max={100}
                      precision={2}
                    />
                  </div>
                </div>
              ))}
            </Collapse.Panel>
          ))}
        </Collapse>
      </div>
      {!dialogVisible ? (
        <Button
          type="primary"
          style={{ marginTop: 10 }}
          onClick={() => {
            setDialogVisible(true);
            globalState.set(state => ({
              ...state,
              chooseFigureMetropolitanMode: true,
              currentChooseFigure: null,
              predictionOpened: true,
            }));
          }}
        >
          {t('添加')}
        </Button>
      ) : (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            zIndex: 4,
            position: 'relative',
            marginTop: 10,
          }}
        >
          <Button
            type="primary"
            onClick={() => {
              setDialogVisible(false);
              globalState.set(state => ({
                ...state,
                chooseFigureMetropolitanMode: false,
                currentChooseFigure: null,
              }));
            }}
          >
            {t('取消')}
          </Button>
          <span className={s.cancelDescribe}>{t('点击图元完成添加')}</span>
        </div>
      )}
    </div>
  );
}
